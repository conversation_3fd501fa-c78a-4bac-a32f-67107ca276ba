#!/bin/bash
set -e

# Mem0 权限自动管理脚本 - Init容器版本
# 在所有服务启动前自动处理数据目录权限

echo "🔧 Mem0 自动权限管理 - Init容器"
echo "=================================="

# 数据目录路径
DATA_ROOT="/data"

# 服务用户映射
MEM0_UID=1000
MEM0_GID=1000
QDRANT_UID=1000
QDRANT_GID=1000
NEO4J_UID=7474
NEO4J_GID=7474

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_warn() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
}

# 创建目录结构
create_directories() {
    log_info "创建数据目录结构..."
    
    # 创建主要目录
    mkdir -p "$DATA_ROOT"/{mem0,qdrant,neo4j/{data,logs,import,plugins}}
    
    # 创建Mem0子目录
    mkdir -p "$DATA_ROOT/mem0"/{.mem0,vector_store}
    
    log_info "目录结构创建完成"
}

# 设置权限
set_permissions() {
    log_info "设置服务权限..."
    
    # Mem0 权限
    if [[ -d "$DATA_ROOT/mem0" ]]; then
        log_info "设置 Mem0 权限 (${MEM0_UID}:${MEM0_GID})"
        chown -R $MEM0_UID:$MEM0_GID "$DATA_ROOT/mem0"
        chmod -R 755 "$DATA_ROOT/mem0"
    fi
    
    # Qdrant 权限
    if [[ -d "$DATA_ROOT/qdrant" ]]; then
        log_info "设置 Qdrant 权限 (${QDRANT_UID}:${QDRANT_GID})"
        chown -R $QDRANT_UID:$QDRANT_GID "$DATA_ROOT/qdrant"
        chmod -R 755 "$DATA_ROOT/qdrant"
    fi
    
    # Neo4j 权限
    if [[ -d "$DATA_ROOT/neo4j" ]]; then
        log_info "设置 Neo4j 权限 (${NEO4J_UID}:${NEO4J_GID})"
        chown -R $NEO4J_UID:$NEO4J_GID "$DATA_ROOT/neo4j"
        chmod -R 755 "$DATA_ROOT/neo4j"
        
        # Neo4j特殊权限设置
        chmod 700 "$DATA_ROOT/neo4j/import" 2>/dev/null || true
    fi
}

# 验证权限
verify_permissions() {
    log_info "验证权限设置..."
    
    local errors=0
    
    # 检查Mem0权限
    if [[ -d "$DATA_ROOT/mem0" ]]; then
        local mem0_owner=$(stat -c "%u:%g" "$DATA_ROOT/mem0" 2>/dev/null || echo "unknown")
        if [[ "$mem0_owner" == "$MEM0_UID:$MEM0_GID" ]]; then
            log_info "✓ Mem0 权限正确 ($mem0_owner)"
        else
            log_warn "✗ Mem0 权限异常 ($mem0_owner), 预期: $MEM0_UID:$MEM0_GID"
            ((errors++))
        fi
    fi
    
    # 检查Qdrant权限
    if [[ -d "$DATA_ROOT/qdrant" ]]; then
        local qdrant_owner=$(stat -c "%u:%g" "$DATA_ROOT/qdrant" 2>/dev/null || echo "unknown")
        if [[ "$qdrant_owner" == "$QDRANT_UID:$QDRANT_GID" ]]; then
            log_info "✓ Qdrant 权限正确 ($qdrant_owner)"
        else
            log_warn "✗ Qdrant 权限异常 ($qdrant_owner), 预期: $QDRANT_UID:$QDRANT_GID"
            ((errors++))
        fi
    fi
    
    # 检查Neo4j权限
    if [[ -d "$DATA_ROOT/neo4j" ]]; then
        local neo4j_owner=$(stat -c "%u:%g" "$DATA_ROOT/neo4j" 2>/dev/null || echo "unknown")
        if [[ "$neo4j_owner" == "$NEO4J_UID:$NEO4J_GID" ]]; then
            log_info "✓ Neo4j 权限正确 ($neo4j_owner)"
        else
            log_warn "✗ Neo4j 权限异常 ($neo4j_owner), 预期: $NEO4J_UID:$NEO4J_GID"
            ((errors++))
        fi
    fi
    
    if [[ $errors -eq 0 ]]; then
        log_info "✅ 所有权限验证通过"
        return 0
    else
        log_error "❌ 发现 $errors 个权限问题"
        return 1
    fi
}

# 创建权限标记文件
create_marker() {
    local marker_file="$DATA_ROOT/.permissions_initialized"
    echo "# Mem0 权限初始化完成" > "$marker_file"
    echo "# 时间: $(date)" >> "$marker_file"
    echo "# Init容器版本: 1.0" >> "$marker_file"
    chmod 644 "$marker_file"
    log_info "权限初始化标记已创建"
}

# 主执行函数
main() {
    log_info "开始权限初始化..."
    
    # 检查是否已初始化
    if [[ -f "$DATA_ROOT/.permissions_initialized" ]]; then
        log_info "检测到权限已初始化，进行验证..."
        
        if verify_permissions; then
            log_info "权限验证通过，跳过初始化"
            exit 0
        else
            log_warn "权限验证失败，重新初始化..."
        fi
    fi
    
    # 执行初始化
    create_directories
    set_permissions
    
    # 验证结果
    if verify_permissions; then
        create_marker
        log_info "🎉 权限初始化完成！"
    else
        log_error "权限初始化失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
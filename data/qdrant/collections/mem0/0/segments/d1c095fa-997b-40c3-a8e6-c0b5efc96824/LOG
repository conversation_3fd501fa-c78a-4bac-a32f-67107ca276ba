2025/07/30-21:20:16.022434 21 RocksDB version: 8.10.0
2025/07/30-21:20:16.022995 21 Compile date 2023-12-15 13:01:14
2025/07/30-21:20:16.023003 21 DB SUMMARY
2025/07/30-21:20:16.023006 21 Host name (Env):  8d8d59995270
2025/07/30-21:20:16.023008 21 DB Session ID:  E8Z4CNLM3P39RY6UTOS6
2025/07/30-21:20:16.023047 21 SST files in ./storage/collections/mem0/0/segments/d1c095fa-997b-40c3-a8e6-c0b5efc96824 dir, Total Num: 0, files: 
2025/07/30-21:20:16.023053 21 Write Ahead Log file in ./storage/collections/mem0/0/segments/d1c095fa-997b-40c3-a8e6-c0b5efc96824: 
2025/07/30-21:20:16.023055 21                         Options.error_if_exists: 0
2025/07/30-21:20:16.023057 21                       Options.create_if_missing: 1
2025/07/30-21:20:16.023059 21                         Options.paranoid_checks: 1
2025/07/30-21:20:16.023060 21             Options.flush_verify_memtable_count: 1
2025/07/30-21:20:16.023062 21          Options.compaction_verify_record_count: 1
2025/07/30-21:20:16.023064 21                               Options.track_and_verify_wals_in_manifest: 0
2025/07/30-21:20:16.023066 21        Options.verify_sst_unique_id_in_manifest: 1
2025/07/30-21:20:16.023073 21                                     Options.env: 0x743cf40014c0
2025/07/30-21:20:16.023075 21                                      Options.fs: PosixFileSystem
2025/07/30-21:20:16.023077 21                                Options.info_log: 0x743cf400c4d0
2025/07/30-21:20:16.023078 21                Options.max_file_opening_threads: 16
2025/07/30-21:20:16.023080 21                              Options.statistics: (nil)
2025/07/30-21:20:16.023082 21                               Options.use_fsync: 0
2025/07/30-21:20:16.023087 21                       Options.max_log_file_size: 1048576
2025/07/30-21:20:16.023089 21                  Options.max_manifest_file_size: 1073741824
2025/07/30-21:20:16.023091 21                   Options.log_file_time_to_roll: 0
2025/07/30-21:20:16.023092 21                       Options.keep_log_file_num: 1
2025/07/30-21:20:16.023094 21                    Options.recycle_log_file_num: 0
2025/07/30-21:20:16.023096 21                         Options.allow_fallocate: 1
2025/07/30-21:20:16.023097 21                        Options.allow_mmap_reads: 0
2025/07/30-21:20:16.023098 21                       Options.allow_mmap_writes: 0
2025/07/30-21:20:16.023100 21                        Options.use_direct_reads: 0
2025/07/30-21:20:16.023101 21                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/30-21:20:16.023103 21          Options.create_missing_column_families: 1
2025/07/30-21:20:16.023104 21                              Options.db_log_dir: 
2025/07/30-21:20:16.023106 21                                 Options.wal_dir: 
2025/07/30-21:20:16.023108 21                Options.table_cache_numshardbits: 6
2025/07/30-21:20:16.023110 21                         Options.WAL_ttl_seconds: 0
2025/07/30-21:20:16.023111 21                       Options.WAL_size_limit_MB: 0
2025/07/30-21:20:16.023113 21                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/30-21:20:16.023115 21             Options.manifest_preallocation_size: 4194304
2025/07/30-21:20:16.023117 21                     Options.is_fd_close_on_exec: 1
2025/07/30-21:20:16.023119 21                   Options.advise_random_on_open: 1
2025/07/30-21:20:16.023121 21                    Options.db_write_buffer_size: 0
2025/07/30-21:20:16.023123 21                    Options.write_buffer_manager: 0x743cf400bf20
2025/07/30-21:20:16.023125 21         Options.access_hint_on_compaction_start: 1
2025/07/30-21:20:16.023127 21           Options.random_access_max_buffer_size: 1048576
2025/07/30-21:20:16.023129 21                      Options.use_adaptive_mutex: 0
2025/07/30-21:20:16.023134 21                            Options.rate_limiter: (nil)
2025/07/30-21:20:16.023136 21     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/30-21:20:16.023137 21                       Options.wal_recovery_mode: 0
2025/07/30-21:20:16.023147 21                  Options.enable_thread_tracking: 0
2025/07/30-21:20:16.023150 21                  Options.enable_pipelined_write: 0
2025/07/30-21:20:16.023154 21                  Options.unordered_write: 0
2025/07/30-21:20:16.023157 21         Options.allow_concurrent_memtable_write: 1
2025/07/30-21:20:16.023158 21      Options.enable_write_thread_adaptive_yield: 1
2025/07/30-21:20:16.023160 21             Options.write_thread_max_yield_usec: 100
2025/07/30-21:20:16.023162 21            Options.write_thread_slow_yield_usec: 3
2025/07/30-21:20:16.023164 21                               Options.row_cache: None
2025/07/30-21:20:16.023166 21                              Options.wal_filter: None
2025/07/30-21:20:16.023175 21             Options.avoid_flush_during_recovery: 0
2025/07/30-21:20:16.023176 21             Options.allow_ingest_behind: 0
2025/07/30-21:20:16.023178 21             Options.two_write_queues: 0
2025/07/30-21:20:16.023180 21             Options.manual_wal_flush: 0
2025/07/30-21:20:16.023181 21             Options.wal_compression: 0
2025/07/30-21:20:16.023183 21             Options.atomic_flush: 0
2025/07/30-21:20:16.023184 21             Options.avoid_unnecessary_blocking_io: 0
2025/07/30-21:20:16.023186 21                 Options.persist_stats_to_disk: 0
2025/07/30-21:20:16.023188 21                 Options.write_dbid_to_manifest: 0
2025/07/30-21:20:16.023189 21                 Options.log_readahead_size: 0
2025/07/30-21:20:16.023191 21                 Options.file_checksum_gen_factory: Unknown
2025/07/30-21:20:16.023193 21                 Options.best_efforts_recovery: 0
2025/07/30-21:20:16.023195 21                Options.max_bgerror_resume_count: 2147483647
2025/07/30-21:20:16.023197 21            Options.bgerror_resume_retry_interval: 1000000
2025/07/30-21:20:16.023199 21             Options.allow_data_in_errors: 0
2025/07/30-21:20:16.023201 21             Options.db_host_id: __hostname__
2025/07/30-21:20:16.023202 21             Options.enforce_single_del_contracts: true
2025/07/30-21:20:16.023204 21             Options.max_background_jobs: 2
2025/07/30-21:20:16.023206 21             Options.max_background_compactions: -1
2025/07/30-21:20:16.023208 21             Options.max_subcompactions: 1
2025/07/30-21:20:16.023212 21             Options.avoid_flush_during_shutdown: 0
2025/07/30-21:20:16.023214 21           Options.writable_file_max_buffer_size: 1048576
2025/07/30-21:20:16.023216 21             Options.delayed_write_rate : 16777216
2025/07/30-21:20:16.023220 21             Options.max_total_wal_size: 0
2025/07/30-21:20:16.023222 21             Options.delete_obsolete_files_period_micros: 180000000
2025/07/30-21:20:16.023223 21                   Options.stats_dump_period_sec: 600
2025/07/30-21:20:16.023226 21                 Options.stats_persist_period_sec: 600
2025/07/30-21:20:16.023227 21                 Options.stats_history_buffer_size: 1048576
2025/07/30-21:20:16.023229 21                          Options.max_open_files: 256
2025/07/30-21:20:16.023231 21                          Options.bytes_per_sync: 0
2025/07/30-21:20:16.023232 21                      Options.wal_bytes_per_sync: 0
2025/07/30-21:20:16.023234 21                   Options.strict_bytes_per_sync: 0
2025/07/30-21:20:16.023236 21       Options.compaction_readahead_size: 2097152
2025/07/30-21:20:16.023238 21                  Options.max_background_flushes: -1
2025/07/30-21:20:16.023239 21 Options.daily_offpeak_time_utc: 
2025/07/30-21:20:16.023241 21 Compression algorithms supported:
2025/07/30-21:20:16.023243 21 	kZSTD supported: 0
2025/07/30-21:20:16.023244 21 	kXpressCompression supported: 0
2025/07/30-21:20:16.023246 21 	kBZip2Compression supported: 0
2025/07/30-21:20:16.023248 21 	kZSTDNotFinalCompression supported: 0
2025/07/30-21:20:16.023249 21 	kLZ4Compression supported: 1
2025/07/30-21:20:16.023251 21 	kZlibCompression supported: 0
2025/07/30-21:20:16.023252 21 	kLZ4HCCompression supported: 1
2025/07/30-21:20:16.023254 21 	kSnappyCompression supported: 1
2025/07/30-21:20:16.023257 21 Fast CRC32 supported: Not supported on x86
2025/07/30-21:20:16.023259 21 DMutex implementation: pthread_mutex_t
2025/07/30-21:20:16.033826 21               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.033836 21           Options.merge_operator: None
2025/07/30-21:20:16.033838 21        Options.compaction_filter: None
2025/07/30-21:20:16.033840 21        Options.compaction_filter_factory: None
2025/07/30-21:20:16.033842 21  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.033844 21         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.033845 21            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.033893 21            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cf40024f0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cf4002820
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.033896 21        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.033898 21  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.033900 21          Options.compression: LZ4
2025/07/30-21:20:16.033903 21                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.033904 21       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.033906 21   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.033908 21             Options.num_levels: 7
2025/07/30-21:20:16.033910 21        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.033911 21     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.033913 21     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.033915 21            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.033917 21                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.033919 21               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.033921 21         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.033923 21         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.033925 21         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.033926 21                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.033928 21         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.033930 21         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.033931 21            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.033934 21                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.033936 21               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.033938 21         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.033939 21         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.033941 21         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.033943 21         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.033944 21                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.033946 21         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.033950 21      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.033952 21          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.033953 21              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.033959 21                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.033960 21             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.033963 21                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.033980 21 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.033983 21          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.033986 21 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.033988 21 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.033989 21 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.033991 21 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.033993 21 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.033994 21 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.033996 21 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.033997 21       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.033999 21                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.034000 21   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.034002 21                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.034004 21   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.034010 21   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.034012 21                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.034014 21                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.034016 21                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.034018 21 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.034019 21 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.034021 21 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.034023 21 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.034024 21 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.034026 21 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.034028 21 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.034029 21 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.034034 21                   Options.table_properties_collectors: 
2025/07/30-21:20:16.034036 21                   Options.inplace_update_support: 0
2025/07/30-21:20:16.034038 21                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.034040 21               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.034042 21               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.034044 21   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.034046 21                           Options.bloom_locality: 0
2025/07/30-21:20:16.034047 21                    Options.max_successive_merges: 0
2025/07/30-21:20:16.034049 21                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.034051 21                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.034052 21                Options.force_consistency_checks: 1
2025/07/30-21:20:16.034054 21                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.034056 21                               Options.ttl: 2592000
2025/07/30-21:20:16.034058 21          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.034059 21                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.034061 21  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.034064 21    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.034068 21                       Options.enable_blob_files: false
2025/07/30-21:20:16.034070 21                           Options.min_blob_size: 0
2025/07/30-21:20:16.034071 21                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.034073 21                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.034075 21          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.034077 21      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.034080 21 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.034082 21          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.034084 21                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.034085 21         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.034087 21            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.041006 21               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.041016 21           Options.merge_operator: None
2025/07/30-21:20:16.041025 21        Options.compaction_filter: None
2025/07/30-21:20:16.041026 21        Options.compaction_filter_factory: None
2025/07/30-21:20:16.041028 21  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.041029 21         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.041031 21            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.041071 21            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cf40024f0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cf4002820
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.041073 21        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.041075 21  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.041077 21          Options.compression: LZ4
2025/07/30-21:20:16.041078 21                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.041080 21       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.041082 21   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.041083 21             Options.num_levels: 7
2025/07/30-21:20:16.041084 21        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.041086 21     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.041087 21     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.041089 21            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.041090 21                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.041092 21               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.041094 21         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.041095 21         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.041097 21         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.041098 21                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.041100 21         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.041101 21         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.041103 21            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.041105 21                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.041106 21               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.041108 21         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.041110 21         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.041111 21         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.041112 21         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.041114 21                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.041115 21         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.041117 21      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.041118 21          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.041120 21              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.041122 21                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.041123 21             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.041125 21                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.041126 21 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.041129 21          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.041132 21 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.041133 21 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.041134 21 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.041136 21 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.041138 21 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.041139 21 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.041140 21 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.041141 21       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.041143 21                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.041146 21   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.041148 21                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.041152 21   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.041153 21   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.041161 21                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.041164 21                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.041166 21                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.041168 21 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.041170 21 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.041171 21 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.041173 21 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.041174 21 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.041176 21 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.041177 21 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.041179 21 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.041185 21                   Options.table_properties_collectors: 
2025/07/30-21:20:16.041189 21                   Options.inplace_update_support: 0
2025/07/30-21:20:16.041191 21                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.041192 21               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.041194 21               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.041196 21   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.041197 21                           Options.bloom_locality: 0
2025/07/30-21:20:16.041198 21                    Options.max_successive_merges: 0
2025/07/30-21:20:16.041200 21                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.041201 21                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.041203 21                Options.force_consistency_checks: 1
2025/07/30-21:20:16.041204 21                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.041205 21                               Options.ttl: 2592000
2025/07/30-21:20:16.041207 21          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.041210 21                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.041213 21  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.041214 21    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.041216 21                       Options.enable_blob_files: false
2025/07/30-21:20:16.041217 21                           Options.min_blob_size: 0
2025/07/30-21:20:16.041219 21                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.041220 21                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.041222 21          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.041224 21      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.041226 21 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.041228 21          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.041230 21                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.041231 21         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.041233 21            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.042667 21               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.042686 21           Options.merge_operator: None
2025/07/30-21:20:16.042691 21        Options.compaction_filter: None
2025/07/30-21:20:16.042695 21        Options.compaction_filter_factory: None
2025/07/30-21:20:16.042697 21  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.042699 21         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.042701 21            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.042743 21            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cf40024f0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cf4002820
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.042746 21        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.042747 21  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.042750 21          Options.compression: LZ4
2025/07/30-21:20:16.042751 21                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.042752 21       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.042754 21   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.042755 21             Options.num_levels: 7
2025/07/30-21:20:16.042757 21        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.042758 21     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.042760 21     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.042761 21            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.042763 21                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.042765 21               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.042766 21         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.042768 21         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.042770 21         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.042771 21                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.042773 21         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.042775 21         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.042777 21            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.042779 21                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.042780 21               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.042782 21         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.042783 21         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.042785 21         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.042786 21         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.042788 21                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.042791 21         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.042793 21      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.042795 21          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.042797 21              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.042798 21                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.042800 21             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.042801 21                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.042802 21 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.042805 21          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.042807 21 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.042808 21 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.042809 21 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.042811 21 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.042812 21 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.042813 21 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.042815 21 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.042816 21       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.042817 21                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.042818 21   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.042820 21                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.042821 21   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.042823 21   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.042824 21                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.042826 21                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.042827 21                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.042829 21 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.042830 21 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.042832 21 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.042833 21 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.042836 21 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.042838 21 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.042840 21 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.042841 21 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.042848 21                   Options.table_properties_collectors: 
2025/07/30-21:20:16.042853 21                   Options.inplace_update_support: 0
2025/07/30-21:20:16.042854 21                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.042856 21               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.042858 21               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.042859 21   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.042861 21                           Options.bloom_locality: 0
2025/07/30-21:20:16.042862 21                    Options.max_successive_merges: 0
2025/07/30-21:20:16.042864 21                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.042866 21                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.042867 21                Options.force_consistency_checks: 1
2025/07/30-21:20:16.042868 21                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.042870 21                               Options.ttl: 2592000
2025/07/30-21:20:16.042871 21          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.042873 21                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.042874 21  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.042876 21    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.042877 21                       Options.enable_blob_files: false
2025/07/30-21:20:16.042879 21                           Options.min_blob_size: 0
2025/07/30-21:20:16.042880 21                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.042882 21                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.042883 21          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.042886 21      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.042888 21 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.042890 21          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.042892 21                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.042894 21         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.042898 21            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.044432 21               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.044453 21           Options.merge_operator: None
2025/07/30-21:20:16.044458 21        Options.compaction_filter: None
2025/07/30-21:20:16.044462 21        Options.compaction_filter_factory: None
2025/07/30-21:20:16.044464 21  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.044466 21         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.044467 21            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.044503 21            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cf40024f0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cf4002820
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.044506 21        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.044509 21  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.044510 21          Options.compression: LZ4
2025/07/30-21:20:16.044512 21                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.044513 21       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.044515 21   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.044516 21             Options.num_levels: 7
2025/07/30-21:20:16.044518 21        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.044520 21     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.044521 21     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.044522 21            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.044524 21                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.044525 21               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.044527 21         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.044528 21         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.044529 21         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.044531 21                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.044532 21         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.044534 21         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.044535 21            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.044537 21                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.044538 21               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.044539 21         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.044541 21         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.044542 21         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.044543 21         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.044546 21                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.044548 21         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.044550 21      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.044551 21          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.044552 21              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.044554 21                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.044555 21             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.044557 21                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.044558 21 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.044561 21          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.044570 21 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.044572 21 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.044573 21 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.044575 21 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.044576 21 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.044588 21 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.044590 21 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.044591 21       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.044593 21                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.044594 21   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.044596 21                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.044597 21   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.044599 21   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.044600 21                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.044602 21                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.044604 21                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.044605 21 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.044606 21 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.044608 21 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.044609 21 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.044615 21 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.044617 21 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.044619 21 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.044620 21 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.044624 21                   Options.table_properties_collectors: 
2025/07/30-21:20:16.044639 21                   Options.inplace_update_support: 0
2025/07/30-21:20:16.044641 21                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.044643 21               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.044645 21               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.044647 21   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.044648 21                           Options.bloom_locality: 0
2025/07/30-21:20:16.044650 21                    Options.max_successive_merges: 0
2025/07/30-21:20:16.044651 21                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.044652 21                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.044654 21                Options.force_consistency_checks: 1
2025/07/30-21:20:16.044655 21                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.044656 21                               Options.ttl: 2592000
2025/07/30-21:20:16.044658 21          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.044659 21                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.044660 21  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.044662 21    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.044664 21                       Options.enable_blob_files: false
2025/07/30-21:20:16.044665 21                           Options.min_blob_size: 0
2025/07/30-21:20:16.044666 21                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.044668 21                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.044670 21          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.044672 21      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.044674 21 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.044675 21          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.044677 21                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.044680 21         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.044682 21            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.046190 21               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.046207 21           Options.merge_operator: None
2025/07/30-21:20:16.046209 21        Options.compaction_filter: None
2025/07/30-21:20:16.046210 21        Options.compaction_filter_factory: None
2025/07/30-21:20:16.046213 21  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.046214 21         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.046216 21            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.046250 21            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cf40024f0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cf4002820
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.046253 21        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.046255 21  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.046256 21          Options.compression: LZ4
2025/07/30-21:20:16.046258 21                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.046259 21       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.046261 21   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.046262 21             Options.num_levels: 7
2025/07/30-21:20:16.046263 21        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.046265 21     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.046267 21     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.046269 21            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.046270 21                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.046271 21               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.046273 21         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.046274 21         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.046276 21         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.046277 21                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.046278 21         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.046280 21         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.046282 21            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.046283 21                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.046284 21               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.046290 21         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.046292 21         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.046293 21         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.046294 21         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.046296 21                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.046297 21         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.046299 21      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.046301 21          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.046302 21              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.046304 21                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.046305 21             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.046306 21                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.046308 21 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.046310 21          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.046312 21 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.046313 21 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.046315 21 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.046316 21 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.046318 21 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.046319 21 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.046320 21 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.046322 21       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.046323 21                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.046325 21   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.046326 21                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.046327 21   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.046329 21   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.046330 21                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.046332 21                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.046333 21                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.046337 21 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.046339 21 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.046340 21 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.046342 21 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.046343 21 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.046345 21 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.046346 21 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.046348 21 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.046353 21                   Options.table_properties_collectors: 
2025/07/30-21:20:16.046355 21                   Options.inplace_update_support: 0
2025/07/30-21:20:16.046356 21                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.046358 21               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.046360 21               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.046361 21   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.046363 21                           Options.bloom_locality: 0
2025/07/30-21:20:16.046364 21                    Options.max_successive_merges: 0
2025/07/30-21:20:16.046366 21                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.046367 21                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.046369 21                Options.force_consistency_checks: 1
2025/07/30-21:20:16.046371 21                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.046372 21                               Options.ttl: 2592000
2025/07/30-21:20:16.046374 21          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.046376 21                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.046377 21  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.046379 21    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.046381 21                       Options.enable_blob_files: false
2025/07/30-21:20:16.046382 21                           Options.min_blob_size: 0
2025/07/30-21:20:16.046383 21                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.046385 21                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.046387 21          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.046391 21      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.046393 21 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.046394 21          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.046405 21                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.046407 21         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.046409 21            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.053281 21 DB pointer 0x743cf4012480

2025/07/30-21:20:16.022436 20 RocksDB version: 8.10.0
2025/07/30-21:20:16.022998 20 Compile date 2023-12-15 13:01:14
2025/07/30-21:20:16.023001 20 DB SUMMARY
2025/07/30-21:20:16.023004 20 Host name (Env):  8d8d59995270
2025/07/30-21:20:16.023006 20 DB Session ID:  E8Z4CNLM3P39RY6UTOS7
2025/07/30-21:20:16.023047 20 SST files in ./storage/collections/mem0/0/segments/6ef6b6fc-7059-4c41-8184-da0474d73c33 dir, Total Num: 0, files: 
2025/07/30-21:20:16.023057 20 Write Ahead Log file in ./storage/collections/mem0/0/segments/6ef6b6fc-7059-4c41-8184-da0474d73c33: 
2025/07/30-21:20:16.023060 20                         Options.error_if_exists: 0
2025/07/30-21:20:16.023061 20                       Options.create_if_missing: 1
2025/07/30-21:20:16.023063 20                         Options.paranoid_checks: 1
2025/07/30-21:20:16.023064 20             Options.flush_verify_memtable_count: 1
2025/07/30-21:20:16.023066 20          Options.compaction_verify_record_count: 1
2025/07/30-21:20:16.023068 20                               Options.track_and_verify_wals_in_manifest: 0
2025/07/30-21:20:16.023069 20        Options.verify_sst_unique_id_in_manifest: 1
2025/07/30-21:20:16.023073 20                                     Options.env: 0x743cf40014c0
2025/07/30-21:20:16.023075 20                                      Options.fs: PosixFileSystem
2025/07/30-21:20:16.023076 20                                Options.info_log: 0x743cfc00b1d0
2025/07/30-21:20:16.023078 20                Options.max_file_opening_threads: 16
2025/07/30-21:20:16.023079 20                              Options.statistics: (nil)
2025/07/30-21:20:16.023081 20                               Options.use_fsync: 0
2025/07/30-21:20:16.023087 20                       Options.max_log_file_size: 1048576
2025/07/30-21:20:16.023089 20                  Options.max_manifest_file_size: 1073741824
2025/07/30-21:20:16.023090 20                   Options.log_file_time_to_roll: 0
2025/07/30-21:20:16.023092 20                       Options.keep_log_file_num: 1
2025/07/30-21:20:16.023093 20                    Options.recycle_log_file_num: 0
2025/07/30-21:20:16.023095 20                         Options.allow_fallocate: 1
2025/07/30-21:20:16.023096 20                        Options.allow_mmap_reads: 0
2025/07/30-21:20:16.023098 20                       Options.allow_mmap_writes: 0
2025/07/30-21:20:16.023099 20                        Options.use_direct_reads: 0
2025/07/30-21:20:16.023100 20                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/30-21:20:16.023102 20          Options.create_missing_column_families: 1
2025/07/30-21:20:16.023103 20                              Options.db_log_dir: 
2025/07/30-21:20:16.023105 20                                 Options.wal_dir: 
2025/07/30-21:20:16.023110 20                Options.table_cache_numshardbits: 6
2025/07/30-21:20:16.023112 20                         Options.WAL_ttl_seconds: 0
2025/07/30-21:20:16.023113 20                       Options.WAL_size_limit_MB: 0
2025/07/30-21:20:16.023115 20                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/30-21:20:16.023117 20             Options.manifest_preallocation_size: 4194304
2025/07/30-21:20:16.023119 20                     Options.is_fd_close_on_exec: 1
2025/07/30-21:20:16.023121 20                   Options.advise_random_on_open: 1
2025/07/30-21:20:16.023123 20                    Options.db_write_buffer_size: 0
2025/07/30-21:20:16.023125 20                    Options.write_buffer_manager: 0x743cfc00ac50
2025/07/30-21:20:16.023127 20         Options.access_hint_on_compaction_start: 1
2025/07/30-21:20:16.023129 20           Options.random_access_max_buffer_size: 1048576
2025/07/30-21:20:16.023137 20                      Options.use_adaptive_mutex: 0
2025/07/30-21:20:16.023143 20                            Options.rate_limiter: (nil)
2025/07/30-21:20:16.023144 20     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/30-21:20:16.023146 20                       Options.wal_recovery_mode: 0
2025/07/30-21:20:16.023147 20                  Options.enable_thread_tracking: 0
2025/07/30-21:20:16.023149 20                  Options.enable_pipelined_write: 0
2025/07/30-21:20:16.023154 20                  Options.unordered_write: 0
2025/07/30-21:20:16.023156 20         Options.allow_concurrent_memtable_write: 1
2025/07/30-21:20:16.023157 20      Options.enable_write_thread_adaptive_yield: 1
2025/07/30-21:20:16.023159 20             Options.write_thread_max_yield_usec: 100
2025/07/30-21:20:16.023160 20            Options.write_thread_slow_yield_usec: 3
2025/07/30-21:20:16.023162 20                               Options.row_cache: None
2025/07/30-21:20:16.023164 20                              Options.wal_filter: None
2025/07/30-21:20:16.023166 20             Options.avoid_flush_during_recovery: 0
2025/07/30-21:20:16.023170 20             Options.allow_ingest_behind: 0
2025/07/30-21:20:16.023171 20             Options.two_write_queues: 0
2025/07/30-21:20:16.023173 20             Options.manual_wal_flush: 0
2025/07/30-21:20:16.023174 20             Options.wal_compression: 0
2025/07/30-21:20:16.023176 20             Options.atomic_flush: 0
2025/07/30-21:20:16.023177 20             Options.avoid_unnecessary_blocking_io: 0
2025/07/30-21:20:16.023179 20                 Options.persist_stats_to_disk: 0
2025/07/30-21:20:16.023180 20                 Options.write_dbid_to_manifest: 0
2025/07/30-21:20:16.023182 20                 Options.log_readahead_size: 0
2025/07/30-21:20:16.023183 20                 Options.file_checksum_gen_factory: Unknown
2025/07/30-21:20:16.023185 20                 Options.best_efforts_recovery: 0
2025/07/30-21:20:16.023186 20                Options.max_bgerror_resume_count: 2147483647
2025/07/30-21:20:16.023188 20            Options.bgerror_resume_retry_interval: 1000000
2025/07/30-21:20:16.023189 20             Options.allow_data_in_errors: 0
2025/07/30-21:20:16.023192 20             Options.db_host_id: __hostname__
2025/07/30-21:20:16.023194 20             Options.enforce_single_del_contracts: true
2025/07/30-21:20:16.023195 20             Options.max_background_jobs: 2
2025/07/30-21:20:16.023197 20             Options.max_background_compactions: -1
2025/07/30-21:20:16.023199 20             Options.max_subcompactions: 1
2025/07/30-21:20:16.023201 20             Options.avoid_flush_during_shutdown: 0
2025/07/30-21:20:16.023202 20           Options.writable_file_max_buffer_size: 1048576
2025/07/30-21:20:16.023204 20             Options.delayed_write_rate : 16777216
2025/07/30-21:20:16.023206 20             Options.max_total_wal_size: 0
2025/07/30-21:20:16.023208 20             Options.delete_obsolete_files_period_micros: 180000000
2025/07/30-21:20:16.023216 20                   Options.stats_dump_period_sec: 600
2025/07/30-21:20:16.023223 20                 Options.stats_persist_period_sec: 600
2025/07/30-21:20:16.023225 20                 Options.stats_history_buffer_size: 1048576
2025/07/30-21:20:16.023227 20                          Options.max_open_files: 256
2025/07/30-21:20:16.023228 20                          Options.bytes_per_sync: 0
2025/07/30-21:20:16.023229 20                      Options.wal_bytes_per_sync: 0
2025/07/30-21:20:16.023231 20                   Options.strict_bytes_per_sync: 0
2025/07/30-21:20:16.023233 20       Options.compaction_readahead_size: 2097152
2025/07/30-21:20:16.023235 20                  Options.max_background_flushes: -1
2025/07/30-21:20:16.023237 20 Options.daily_offpeak_time_utc: 
2025/07/30-21:20:16.023238 20 Compression algorithms supported:
2025/07/30-21:20:16.023240 20 	kZSTD supported: 0
2025/07/30-21:20:16.023242 20 	kXpressCompression supported: 0
2025/07/30-21:20:16.023244 20 	kBZip2Compression supported: 0
2025/07/30-21:20:16.023245 20 	kZSTDNotFinalCompression supported: 0
2025/07/30-21:20:16.023247 20 	kLZ4Compression supported: 1
2025/07/30-21:20:16.023248 20 	kZlibCompression supported: 0
2025/07/30-21:20:16.023250 20 	kLZ4HCCompression supported: 1
2025/07/30-21:20:16.023251 20 	kSnappyCompression supported: 1
2025/07/30-21:20:16.023254 20 Fast CRC32 supported: Not supported on x86
2025/07/30-21:20:16.023256 20 DMutex implementation: pthread_mutex_t
2025/07/30-21:20:16.033826 20               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.033840 20           Options.merge_operator: None
2025/07/30-21:20:16.033843 20        Options.compaction_filter: None
2025/07/30-21:20:16.033845 20        Options.compaction_filter_factory: None
2025/07/30-21:20:16.033846 20  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.033848 20         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.033850 20            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.033892 20            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cfc0013a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cfc001710
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.033897 20        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.033899 20  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.033900 20          Options.compression: LZ4
2025/07/30-21:20:16.033903 20                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.033905 20       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.033906 20   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.033908 20             Options.num_levels: 7
2025/07/30-21:20:16.033910 20        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.033911 20     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.033914 20     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.033916 20            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.033917 20                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.033920 20               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.033921 20         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.033923 20         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.033925 20         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.033928 20                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.033930 20         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.033932 20         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.033933 20            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.033936 20                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.033937 20               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.033939 20         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.033940 20         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.033942 20         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.033943 20         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.033945 20                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.033946 20         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.033950 20      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.033952 20          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.033954 20              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.033963 20                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.033968 20             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.033970 20                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.033972 20 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.033983 20          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.033985 20 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.033987 20 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.033989 20 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.033990 20 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.033992 20 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.033993 20 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.033995 20 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.033996 20       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.033998 20                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.033999 20   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.034003 20                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.034006 20   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.034008 20   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.034009 20                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.034012 20                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.034014 20                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.034015 20 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.034017 20 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.034019 20 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.034020 20 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.034022 20 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.034024 20 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.034025 20 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.034027 20 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.034034 20                   Options.table_properties_collectors: 
2025/07/30-21:20:16.034037 20                   Options.inplace_update_support: 0
2025/07/30-21:20:16.034038 20                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.034041 20               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.034043 20               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.034045 20   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.034046 20                           Options.bloom_locality: 0
2025/07/30-21:20:16.034048 20                    Options.max_successive_merges: 0
2025/07/30-21:20:16.034049 20                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.034051 20                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.034053 20                Options.force_consistency_checks: 1
2025/07/30-21:20:16.034054 20                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.034056 20                               Options.ttl: 2592000
2025/07/30-21:20:16.034058 20          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.034060 20                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.034063 20  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.034069 20    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.034070 20                       Options.enable_blob_files: false
2025/07/30-21:20:16.034072 20                           Options.min_blob_size: 0
2025/07/30-21:20:16.034073 20                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.034075 20                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.034077 20          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.034080 20      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.034081 20 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.034083 20          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.034085 20                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.034086 20         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.034088 20            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.041006 20               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.041022 20           Options.merge_operator: None
2025/07/30-21:20:16.041023 20        Options.compaction_filter: None
2025/07/30-21:20:16.041025 20        Options.compaction_filter_factory: None
2025/07/30-21:20:16.041027 20  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.041029 20         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.041030 20            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.041072 20            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cfc0013a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cfc001710
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.041074 20        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.041076 20  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.041078 20          Options.compression: LZ4
2025/07/30-21:20:16.041079 20                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.041081 20       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.041082 20   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.041084 20             Options.num_levels: 7
2025/07/30-21:20:16.041085 20        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.041086 20     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.041088 20     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.041089 20            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.041090 20                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.041093 20               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.041094 20         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.041096 20         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.041097 20         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.041098 20                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.041100 20         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.041101 20         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.041103 20            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.041105 20                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.041107 20               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.041109 20         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.041110 20         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.041112 20         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.041113 20         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.041114 20                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.041116 20         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.041117 20      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.041119 20          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.041120 20              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.041122 20                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.041124 20             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.041125 20                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.041126 20 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.041129 20          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.041132 20 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.041133 20 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.041135 20 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.041137 20 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.041138 20 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.041139 20 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.041141 20 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.041142 20       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.041143 20                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.041145 20   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.041146 20                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.041148 20   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.041160 20   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.041162 20                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.041164 20                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.041166 20                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.041167 20 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.041169 20 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.041171 20 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.041172 20 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.041174 20 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.041176 20 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.041177 20 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.041179 20 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.041185 20                   Options.table_properties_collectors: 
2025/07/30-21:20:16.041193 20                   Options.inplace_update_support: 0
2025/07/30-21:20:16.041194 20                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.041197 20               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.041198 20               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.041201 20   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.041202 20                           Options.bloom_locality: 0
2025/07/30-21:20:16.041203 20                    Options.max_successive_merges: 0
2025/07/30-21:20:16.041205 20                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.041206 20                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.041207 20                Options.force_consistency_checks: 1
2025/07/30-21:20:16.041209 20                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.041210 20                               Options.ttl: 2592000
2025/07/30-21:20:16.041212 20          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.041213 20                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.041215 20  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.041217 20    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.041218 20                       Options.enable_blob_files: false
2025/07/30-21:20:16.041222 20                           Options.min_blob_size: 0
2025/07/30-21:20:16.041224 20                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.041225 20                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.041227 20          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.041228 20      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.041230 20 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.041232 20          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.041234 20                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.041235 20         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.041237 20            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.042667 20               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.042678 20           Options.merge_operator: None
2025/07/30-21:20:16.042680 20        Options.compaction_filter: None
2025/07/30-21:20:16.042694 20        Options.compaction_filter_factory: None
2025/07/30-21:20:16.042696 20  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.042700 20         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.042702 20            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.042744 20            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cfc0013a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cfc001710
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.042747 20        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.042748 20  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.042750 20          Options.compression: LZ4
2025/07/30-21:20:16.042752 20                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.042753 20       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.042754 20   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.042756 20             Options.num_levels: 7
2025/07/30-21:20:16.042757 20        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.042759 20     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.042760 20     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.042761 20            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.042763 20                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.042765 20               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.042767 20         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.042768 20         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.042770 20         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.042771 20                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.042773 20         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.042776 20         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.042778 20            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.042779 20                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.042781 20               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.042782 20         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.042784 20         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.042785 20         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.042786 20         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.042788 20                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.042790 20         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.042793 20      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.042795 20          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.042796 20              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.042798 20                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.042799 20             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.042800 20                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.042802 20 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.042804 20          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.042806 20 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.042808 20 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.042809 20 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.042810 20 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.042812 20 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.042813 20 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.042814 20 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.042815 20       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.042817 20                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.042818 20   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.042819 20                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.042821 20   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.042822 20   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.042823 20                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.042825 20                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.042827 20                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.042829 20 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.042831 20 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.042832 20 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.042834 20 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.042835 20 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.042837 20 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.042840 20 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.042842 20 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.042848 20                   Options.table_properties_collectors: 
2025/07/30-21:20:16.042857 20                   Options.inplace_update_support: 0
2025/07/30-21:20:16.042858 20                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.042860 20               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.042862 20               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.042864 20   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.042865 20                           Options.bloom_locality: 0
2025/07/30-21:20:16.042867 20                    Options.max_successive_merges: 0
2025/07/30-21:20:16.042868 20                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.042869 20                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.042871 20                Options.force_consistency_checks: 1
2025/07/30-21:20:16.042872 20                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.042873 20                               Options.ttl: 2592000
2025/07/30-21:20:16.042875 20          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.042876 20                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.042878 20  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.042879 20    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.042880 20                       Options.enable_blob_files: false
2025/07/30-21:20:16.042882 20                           Options.min_blob_size: 0
2025/07/30-21:20:16.042884 20                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.042886 20                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.042888 20          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.042890 20      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.042892 20 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.042894 20          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.042895 20                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.042897 20         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.042898 20            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.044431 20               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.044440 20           Options.merge_operator: None
2025/07/30-21:20:16.044442 20        Options.compaction_filter: None
2025/07/30-21:20:16.044462 20        Options.compaction_filter_factory: None
2025/07/30-21:20:16.044463 20  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.044467 20         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.044468 20            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.044502 20            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cfc0013a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cfc001710
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.044504 20        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.044506 20  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.044509 20          Options.compression: LZ4
2025/07/30-21:20:16.044510 20                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.044512 20       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.044513 20   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.044514 20             Options.num_levels: 7
2025/07/30-21:20:16.044516 20        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.044518 20     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.044519 20     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.044521 20            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.044522 20                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.044523 20               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.044525 20         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.044526 20         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.044528 20         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.044529 20                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.044530 20         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.044532 20         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.044533 20            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.044534 20                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.044536 20               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.044537 20         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.044538 20         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.044540 20         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.044541 20         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.044542 20                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.044548 20         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.044550 20      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.044551 20          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.044553 20              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.044554 20                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.044556 20             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.044557 20                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.044558 20 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.044561 20          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.044574 20 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.044576 20 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.044583 20 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.044584 20 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.044586 20 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.044587 20 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.044588 20 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.044590 20       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.044592 20                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.044593 20   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.044595 20                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.044596 20   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.044598 20   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.044599 20                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.044601 20                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.044603 20                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.044604 20 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.044606 20 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.044607 20 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.044609 20 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.044611 20 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.044614 20 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.044617 20 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.044618 20 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.044624 20                   Options.table_properties_collectors: 
2025/07/30-21:20:16.044631 20                   Options.inplace_update_support: 0
2025/07/30-21:20:16.044632 20                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.044643 20               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.044645 20               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.044646 20   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.044648 20                           Options.bloom_locality: 0
2025/07/30-21:20:16.044649 20                    Options.max_successive_merges: 0
2025/07/30-21:20:16.044650 20                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.044652 20                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.044653 20                Options.force_consistency_checks: 1
2025/07/30-21:20:16.044654 20                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.044656 20                               Options.ttl: 2592000
2025/07/30-21:20:16.044657 20          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.044659 20                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.044660 20  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.044662 20    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.044663 20                       Options.enable_blob_files: false
2025/07/30-21:20:16.044665 20                           Options.min_blob_size: 0
2025/07/30-21:20:16.044666 20                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.044668 20                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.044670 20          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.044671 20      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.044673 20 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.044675 20          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.044676 20                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.044678 20         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.044682 20            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.046190 20               Options.comparator: leveldb.BytewiseComparator
2025/07/30-21:20:16.046200 20           Options.merge_operator: None
2025/07/30-21:20:16.046210 20        Options.compaction_filter: None
2025/07/30-21:20:16.046212 20        Options.compaction_filter_factory: None
2025/07/30-21:20:16.046214 20  Options.sst_partitioner_factory: None
2025/07/30-21:20:16.046215 20         Options.memtable_factory: SkipListFactory
2025/07/30-21:20:16.046216 20            Options.table_factory: BlockBasedTable
2025/07/30-21:20:16.046249 20            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x743cfc0013a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x743cfc001710
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/30-21:20:16.046252 20        Options.write_buffer_size: 10485760
2025/07/30-21:20:16.046254 20  Options.max_write_buffer_number: 2
2025/07/30-21:20:16.046256 20          Options.compression: LZ4
2025/07/30-21:20:16.046257 20                  Options.bottommost_compression: Disabled
2025/07/30-21:20:16.046259 20       Options.prefix_extractor: nullptr
2025/07/30-21:20:16.046260 20   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/30-21:20:16.046262 20             Options.num_levels: 7
2025/07/30-21:20:16.046263 20        Options.min_write_buffer_number_to_merge: 1
2025/07/30-21:20:16.046265 20     Options.max_write_buffer_number_to_maintain: 0
2025/07/30-21:20:16.046266 20     Options.max_write_buffer_size_to_maintain: 0
2025/07/30-21:20:16.046268 20            Options.bottommost_compression_opts.window_bits: -14
2025/07/30-21:20:16.046270 20                  Options.bottommost_compression_opts.level: 32767
2025/07/30-21:20:16.046271 20               Options.bottommost_compression_opts.strategy: 0
2025/07/30-21:20:16.046272 20         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.046274 20         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.046275 20         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/30-21:20:16.046277 20                  Options.bottommost_compression_opts.enabled: false
2025/07/30-21:20:16.046278 20         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.046280 20         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.046281 20            Options.compression_opts.window_bits: -14
2025/07/30-21:20:16.046283 20                  Options.compression_opts.level: 32767
2025/07/30-21:20:16.046284 20               Options.compression_opts.strategy: 0
2025/07/30-21:20:16.046286 20         Options.compression_opts.max_dict_bytes: 0
2025/07/30-21:20:16.046287 20         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/30-21:20:16.046292 20         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/30-21:20:16.046294 20         Options.compression_opts.parallel_threads: 1
2025/07/30-21:20:16.046295 20                  Options.compression_opts.enabled: false
2025/07/30-21:20:16.046297 20         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/30-21:20:16.046298 20      Options.level0_file_num_compaction_trigger: 4
2025/07/30-21:20:16.046300 20          Options.level0_slowdown_writes_trigger: 20
2025/07/30-21:20:16.046302 20              Options.level0_stop_writes_trigger: 36
2025/07/30-21:20:16.046303 20                   Options.target_file_size_base: 67108864
2025/07/30-21:20:16.046304 20             Options.target_file_size_multiplier: 1
2025/07/30-21:20:16.046306 20                Options.max_bytes_for_level_base: 268435456
2025/07/30-21:20:16.046307 20 Options.level_compaction_dynamic_level_bytes: 1
2025/07/30-21:20:16.046309 20          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/30-21:20:16.046311 20 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/30-21:20:16.046313 20 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/30-21:20:16.046314 20 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/30-21:20:16.046315 20 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/30-21:20:16.046317 20 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/30-21:20:16.046318 20 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/30-21:20:16.046319 20 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/30-21:20:16.046321 20       Options.max_sequential_skip_in_iterations: 8
2025/07/30-21:20:16.046322 20                    Options.max_compaction_bytes: 1677721600
2025/07/30-21:20:16.046323 20   Options.ignore_max_compaction_bytes_for_input: true
2025/07/30-21:20:16.046325 20                        Options.arena_block_size: 1048576
2025/07/30-21:20:16.046326 20   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/30-21:20:16.046328 20   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/30-21:20:16.046329 20                Options.disable_auto_compactions: 0
2025/07/30-21:20:16.046331 20                        Options.compaction_style: kCompactionStyleLevel
2025/07/30-21:20:16.046333 20                          Options.compaction_pri: kMinOverlappingRatio
2025/07/30-21:20:16.046335 20 Options.compaction_options_universal.size_ratio: 1
2025/07/30-21:20:16.046336 20 Options.compaction_options_universal.min_merge_width: 2
2025/07/30-21:20:16.046338 20 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/30-21:20:16.046341 20 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/30-21:20:16.046342 20 Options.compaction_options_universal.compression_size_percent: -1
2025/07/30-21:20:16.046344 20 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/30-21:20:16.046345 20 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/30-21:20:16.046347 20 Options.compaction_options_fifo.allow_compaction: 0
2025/07/30-21:20:16.046353 20                   Options.table_properties_collectors: 
2025/07/30-21:20:16.046355 20                   Options.inplace_update_support: 0
2025/07/30-21:20:16.046357 20                 Options.inplace_update_num_locks: 10000
2025/07/30-21:20:16.046359 20               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/30-21:20:16.046360 20               Options.memtable_whole_key_filtering: 0
2025/07/30-21:20:16.046362 20   Options.memtable_huge_page_size: 0
2025/07/30-21:20:16.046363 20                           Options.bloom_locality: 0
2025/07/30-21:20:16.046364 20                    Options.max_successive_merges: 0
2025/07/30-21:20:16.046366 20                Options.optimize_filters_for_hits: 0
2025/07/30-21:20:16.046368 20                Options.paranoid_file_checks: 0
2025/07/30-21:20:16.046369 20                Options.force_consistency_checks: 1
2025/07/30-21:20:16.046371 20                Options.report_bg_io_stats: 0
2025/07/30-21:20:16.046372 20                               Options.ttl: 2592000
2025/07/30-21:20:16.046374 20          Options.periodic_compaction_seconds: 0
2025/07/30-21:20:16.046376 20                        Options.default_temperature: kUnknown
2025/07/30-21:20:16.046378 20  Options.preclude_last_level_data_seconds: 0
2025/07/30-21:20:16.046380 20    Options.preserve_internal_time_seconds: 0
2025/07/30-21:20:16.046381 20                       Options.enable_blob_files: false
2025/07/30-21:20:16.046382 20                           Options.min_blob_size: 0
2025/07/30-21:20:16.046383 20                          Options.blob_file_size: 268435456
2025/07/30-21:20:16.046386 20                   Options.blob_compression_type: NoCompression
2025/07/30-21:20:16.046388 20          Options.enable_blob_garbage_collection: false
2025/07/30-21:20:16.046389 20      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/30-21:20:16.046391 20 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/30-21:20:16.046394 20          Options.blob_compaction_readahead_size: 0
2025/07/30-21:20:16.046399 20                Options.blob_file_starting_level: 0
2025/07/30-21:20:16.046401 20         Options.experimental_mempurge_threshold: 0.000000
2025/07/30-21:20:16.046409 20            Options.memtable_max_range_deletions: 0
2025/07/30-21:20:16.051852 20 DB pointer 0x743cfc0111c0

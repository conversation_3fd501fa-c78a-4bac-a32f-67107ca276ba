"use client";

import { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileText } from "lucide-react"
import { useSelector } from "react-redux"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

import InstructionManager from "@/components/mem0/InstructionManager"
import { useConfig } from "@/hooks/useConfig"
import { RootState } from "@/store/store"
import { useToast } from "@/components/ui/use-toast"


export default function SettingsPage() {
  const { toast } = useToast()
  const configState = useSelector((state: RootState) => state.config)

  const { testConnection, isLoading } = useConfig()

  // Add the missing settings state
  const [settings, setSettings] = useState({
    openmemory: { custom_instructions: null },
    mem0: {}
  })

  useEffect(() => {
    // 测试Mem0服务器连接
    const checkConnection = async () => {
      try {
        const isConnected = await testConnection()
        if (!isConnected) {
          toast({
            title: "Connection Warning",
            description: "Unable to connect to Mem0 server",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to test connection",
          variant: "destructive",
        })
      }
    }

    checkConnection()
  }, [testConnection, toast])

  // Update local state when redux state changes
  useEffect(() => {
    setSettings(prev => ({
      ...prev,
      openmemory: configState.openmemory || { custom_instructions: null },
      mem0: configState.mem0
    }))
  }, [configState.openmemory, configState.mem0])

  const handleTestConnection = async () => {
    try {
      const isConnected = await testConnection()
      toast({
        title: isConnected ? "Connection Successful" : "Connection Failed",
        description: isConnected
          ? "Successfully connected to Mem0 server"
          : "Unable to connect to Mem0 server",
        variant: isConnected ? "default" : "destructive",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test connection",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="text-white py-6">
      <div className="container mx-auto py-10 max-w-4xl">
        <div className="flex justify-between items-center mb-8">
          <div className="animate-fade-slide-down">
            <h1 className="text-3xl font-bold tracking-tight">设置</h1>
            <p className="text-muted-foreground mt-1">管理系统配置和指令模板</p>
          </div>
          <div className="flex space-x-2">
            <Button onClick={handleTestConnection} className="bg-primary hover:bg-primary/90 animate-fade-slide-down" disabled={isLoading}>
              <SaveIcon className="mr-2 h-4 w-4" />
              {isLoading ? "测试中..." : "测试连接"}
            </Button>
          </div>
        </div>

        <Tabs defaultValue="connection" className="animate-fade-slide-down delay-1">
          <TabsList className="grid w-full grid-cols-2 bg-zinc-900 border-zinc-800">
            <TabsTrigger value="connection" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              连接设置
            </TabsTrigger>
            <TabsTrigger value="instructions" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              指令管理
            </TabsTrigger>
          </TabsList>

          <TabsContent value="connection" className="mt-6">
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader>
                <CardTitle className="text-white">Mem0 服务器连接</CardTitle>
                <CardDescription className="text-zinc-400">
                  配置与 Mem0 服务器的连接，用于核心记忆管理功能。
                  配置通过环境变量处理。
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-zinc-400">服务器 URL</label>
                    <p className="text-sm text-zinc-300 mt-1">
                      {process.env.NEXT_PUBLIC_MEM0_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-zinc-400">状态</label>
                    <p className="text-sm text-zinc-300 mt-1">
                      点击&quot;测试连接&quot;验证服务器连接性
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="instructions" className="mt-6">
            <InstructionManager />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

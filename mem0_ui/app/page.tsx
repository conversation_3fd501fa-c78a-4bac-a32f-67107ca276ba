"use client";

import React, { useState, useCallback } from 'react';

import Mem0StatsDashboard from "@/components/mem0/Mem0StatsDashboard";
import ActivityTimeline from "@/components/mem0/ActivityTimeline";
import { MemoryFilters } from "@/app/memories/components/MemoryFilters";
import { MemoriesSection } from "@/app/memories/components/MemoriesSection";
import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from "@/components/common/ErrorBoundary";
import { accessibility } from "@/lib/utils";
import "@/styles/animation.css";

export default function DashboardPage() {
  const [currentUserId, setCurrentUserId] = useState<string>('');
  const [currentRunId, setCurrentRunId] = useState<string>('');
  const [currentAgentId, setCurrentAgentId] = useState<string>('');
  const [filterCategories, setFilterCategories] = useState<string[]>([]);



  const handleFiltersChange = useCallback((filters: {
    userId?: string;
    runId?: string;
    agentId?: string;
    categories?: string[];
  }) => {
    if (filters.userId !== undefined) setCurrentUserId(filters.userId);
    if (filters.runId !== undefined) setCurrentRunId(filters.runId);
    if (filters.agentId !== undefined) setCurrentAgentId(filters.agentId);
    if (filters.categories !== undefined) setFilterCategories(filters.categories);
  }, []);

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-black text-white">
        <div className="container-responsive py-8">
          <div className="w-full mx-auto space-responsive">
            {/* Mem0 统计面板 */}
            <section className="animate-fade-slide-down" aria-labelledby="stats-heading">
              <h2 id="stats-heading" className="sr-only">系统统计概览</h2>
              <ErrorBoundary resetOnPropsChange>
                <Mem0StatsDashboard />
              </ErrorBoundary>
            </section>

            {/* 活动时间线 */}
            <section className="animate-fade-slide-down" aria-labelledby="timeline-heading">
              <h2 id="timeline-heading" className="sr-only">活动时间线</h2>
              <ErrorBoundary resetOnPropsChange>
                <ActivityTimeline
                  filterUserId={currentUserId}
                  filterRunId={currentRunId}
                  filterAgentId={currentAgentId}
                  filterCategories={filterCategories}
                  onFiltersChange={handleFiltersChange}
                />
              </ErrorBoundary>
            </section>

            {/* 记忆管理区域 */}
            <section className="animate-fade-slide-down" aria-labelledby="memory-heading">
              <div className="mb-6">
                <h2 id="memory-heading" className="text-responsive-xl font-semibold text-white">
                  记忆管理
                </h2>
                <p className="text-zinc-400 text-responsive-sm">
                  浏览和管理所有记忆数据
                </p>
              </div>
              <div className="space-responsive">
                <ErrorBoundary resetOnPropsChange>
                  <MemoryFilters />
                </ErrorBoundary>
                <ErrorBoundary resetOnPropsChange>
                  <MemoriesSection />
                </ErrorBoundary>
              </div>
            </section>
          </div>
        </div>

        <Toaster />
      </div>
    </ErrorBoundary>
  );
}

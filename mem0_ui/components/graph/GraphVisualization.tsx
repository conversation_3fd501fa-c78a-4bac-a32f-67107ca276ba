'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ReactFlow,
  Controls,
  MiniMap,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  BackgroundVariant,
  Panel,
  useReactFlow,
  ReactFlowProvider
} from 'reactflow';
import 'reactflow/dist/style.css';

import { useSelector, useDispatch } from 'react-redux';
import {
  LayoutGrid,
  Maximize2,
  Minimize2,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Move3D,
  Network,
  GitBranch,
  Circle
} from 'lucide-react';

import { RootState } from '@/store/store';
import { selectNode, deselectNode, selectEdge, deselectEdge, clearSelection, updateViewState } from '@/store/graphMemorySlice';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/components/ui/use-mobile';
import GraphPerformanceManager from '@/lib/performance/GraphPerformanceManager';
import GraphErrorBoundary from '@/components/common/GraphErrorBoundary';

import dynamic from 'next/dynamic';
import { sharedNodeTypes, sharedEdgeTypes } from './shared-types';

// 动态导入移动端界面，只在需要时加载
const MobileGraphInterface = dynamic(() => import('./MobileGraphInterface'), {
  ssr: false,
  loading: () => <div className="h-[600px] bg-zinc-950 rounded-lg border border-zinc-800 flex items-center justify-center">
    <div className="text-zinc-400">Loading mobile interface...</div>
  </div>
});

interface GraphVisualizationProps {
  className?: string;
  height?: string | number;
  onNodeClick?: (node: Node) => void;
  onEdgeClick?: (edge: Edge) => void;
  onSelectionChange?: (selectedNodes: Node[], selectedEdges: Edge[]) => void;
}

const GraphVisualizationInner: React.FC<GraphVisualizationProps> = ({
  className = '',
  height = '600px',
  onNodeClick,
  onEdgeClick,
  onSelectionChange
}) => {
  const dispatch = useDispatch();
  const { fitView, zoomIn, zoomOut, zoomTo } = useReactFlow();
  const isMobile = useIsMobile();

  // 性能管理器
  const performanceManager = useMemo(() => GraphPerformanceManager.getInstance(), []);
  const [renderingStrategy, setRenderingStrategy] = useState<any>(null);
  const [performanceStats, setPerformanceStats] = useState<any>(null);

  // Redux状态
  const {
    nodes: reduxNodes,
    edges: reduxEdges,
    selectedNodeIds,
    selectedEdgeIds,
    viewState
  } = useSelector((state: RootState) => state.graphMemory);

  // React Flow状态
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 初始化性能管理
  useEffect(() => {
    const initPerformance = async () => {
      const strategy = await performanceManager.selectRenderingStrategy();
      setRenderingStrategy(strategy);

      // 定期更新性能统计
      const interval = setInterval(() => {
        const stats = performanceManager.getPerformanceStats();
        setPerformanceStats(stats);
      }, 5000);

      return () => clearInterval(interval);
    };

    initPerformance();
  }, [performanceManager]);

  // 同步Redux数据到React Flow
  useEffect(() => {
    if (reduxNodes && reduxNodes.length > 0) {
      setNodes(reduxNodes.map((node: Node) => ({
        ...node,
        type: 'graphNode',
        selected: selectedNodeIds.includes(node.id)
      })));
    }
  }, [reduxNodes, selectedNodeIds, setNodes]);

  useEffect(() => {
    if (reduxEdges && reduxEdges.length > 0) {
      setEdges(reduxEdges.map((edge: Edge) => ({
        ...edge,
        type: 'graphEdge',
        selected: selectedEdgeIds.includes(edge.id)
      })));
    }
  }, [reduxEdges, selectedEdgeIds, setEdges]);

  // 处理连接创建
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // 处理节点选择（支持Ctrl+点击多选）
  const onNodeClickHandler = useCallback((event: React.MouseEvent, node: Node) => {
    event.stopPropagation();

    // 检查是否按住Ctrl/Cmd键进行多选
    const isMultiSelect = event.ctrlKey || event.metaKey;

    if (isMultiSelect) {
      // 多选模式：切换选择状态
      if (selectedNodeIds.includes(node.id)) {
        dispatch(deselectNode(node.id));
      } else {
        dispatch(selectNode(node.id));
      }
    } else {
      // 单选模式：清除其他选择，只选择当前节点
      if (selectedNodeIds.includes(node.id) && selectedNodeIds.length === 1) {
        // 如果只选择了当前节点，则取消选择
        dispatch(clearSelection());
      } else {
        // 清除所有选择，然后选择当前节点
        dispatch(clearSelection());
        dispatch(selectNode(node.id));
      }
    }

    onNodeClick?.(node);
  }, [dispatch, selectedNodeIds, onNodeClick]);

  // 处理边选择（支持Ctrl+点击多选）
  const onEdgeClickHandler = useCallback((event: React.MouseEvent, edge: Edge) => {
    event.stopPropagation();

    // 检查是否按住Ctrl/Cmd键进行多选
    const isMultiSelect = event.ctrlKey || event.metaKey;

    if (isMultiSelect) {
      // 多选模式：切换选择状态
      if (selectedEdgeIds.includes(edge.id)) {
        dispatch(deselectEdge(edge.id));
      } else {
        dispatch(selectEdge(edge.id));
      }
    } else {
      // 单选模式：清除其他选择，只选择当前边
      if (selectedEdgeIds.includes(edge.id) && selectedEdgeIds.length === 1) {
        // 如果只选择了当前边，则取消选择
        dispatch(clearSelection());
      } else {
        // 清除所有选择，然后选择当前边
        dispatch(clearSelection());
        dispatch(selectEdge(edge.id));
      }
    }

    onEdgeClick?.(edge);
  }, [dispatch, selectedEdgeIds, onEdgeClick]);

  // 处理画布点击（清除选择）
  const onPaneClick = useCallback(() => {
    dispatch(clearSelection());
  }, [dispatch]);

  // 布局切换
  const handleLayoutChange = useCallback((layout: 'force' | 'hierarchical' | 'circular' | 'grid') => {
    dispatch(updateViewState({ layout }));
  }, [dispatch]);

  // 视图控制
  const handleFitView = useCallback(() => {
    fitView({ padding: 0.1, duration: 800 });
  }, [fitView]);

  const handleZoomIn = useCallback(() => {
    zoomIn({ duration: 300 });
  }, [zoomIn]);

  const handleZoomOut = useCallback(() => {
    zoomOut({ duration: 300 });
  }, [zoomOut]);

  const handleResetView = useCallback(() => {
    zoomTo(1, { duration: 500 });
    fitView({ padding: 0.1, duration: 800 });
  }, [zoomTo, fitView]);

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  // 选择变化回调
  useEffect(() => {
    const selectedNodes = nodes.filter(node => selectedNodeIds.includes(node.id));
    const selectedEdges = edges.filter(edge => selectedEdgeIds.includes(edge.id));
    onSelectionChange?.(selectedNodes, selectedEdges);
  }, [nodes, edges, selectedNodeIds, selectedEdgeIds, onSelectionChange]);

  // 统计信息
  const stats = useMemo(() => ({
    totalNodes: nodes.length,
    totalEdges: edges.length,
    selectedNodes: selectedNodeIds.length,
    selectedEdges: selectedEdgeIds.length
  }), [nodes.length, edges.length, selectedNodeIds.length, selectedEdgeIds.length]);

  return (
    <div 
      className={`relative bg-zinc-950 rounded-lg border border-zinc-800 overflow-hidden ${
        isFullscreen ? 'fixed inset-0 z-50' : ''
      } ${className}`}
      style={{ height: isFullscreen ? '100vh' : height }}
    >
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClickHandler}
        onEdgeClick={onEdgeClickHandler}
        onPaneClick={onPaneClick}
        nodeTypes={sharedNodeTypes}
        edgeTypes={sharedEdgeTypes}
        fitView
        attributionPosition="bottom-left"
        className="bg-zinc-950"
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        minZoom={0.1}
        maxZoom={4}
        snapToGrid={true}
        snapGrid={[15, 15]}
        // 启用框选功能
        selectionOnDrag={true}
        panOnDrag={[1, 2]} // 只有中键和右键可以拖拽画布
        selectNodesOnDrag={false}
        // 性能优化设置
        nodesDraggable={!isMobile} // 移动端禁用节点拖拽以提升性能
        nodesConnectable={false} // 禁用节点连接以提升性能
        elementsSelectable={true}
        // 硬件加速
        translateExtent={[[-2000, -2000], [2000, 2000]]} // 限制平移范围
        nodeExtent={[[-1500, -1500], [1500, 1500]]} // 限制节点范围
        // 渲染优化
        onlyRenderVisibleElements={renderingStrategy?.enableVirtualization || false}
        // 移动端优化
        panOnScroll={!isMobile}
        zoomOnScroll={!isMobile}
        zoomOnPinch={isMobile}
        panOnScrollMode={isMobile ? undefined : undefined}
        zoomOnDoubleClick={false} // 禁用双击缩放避免误触
        // 框选回调
        onSelectionChange={(params) => {
          // 处理框选的节点和边
          if (params.nodes && params.nodes.length > 0) {
            // 清除当前选择
            dispatch(clearSelection());
            // 选择框选的节点
            params.nodes.forEach(node => {
              dispatch(selectNode(node.id));
            });
          }
          if (params.edges && params.edges.length > 0) {
            // 选择框选的边（如果没有节点被选中）
            if (!params.nodes || params.nodes.length === 0) {
              dispatch(clearSelection());
            }
            params.edges.forEach(edge => {
              dispatch(selectEdge(edge.id));
            });
          }
        }}
      >
        {/* 背景 */}
        <Background 
          variant={BackgroundVariant.Dots} 
          gap={20} 
          size={1}
          color="#374151"
        />
        
        {/* 控制面板 */}
        <Controls 
          className="bg-zinc-900 border border-zinc-700"
          showZoom={true}
          showFitView={true}
          showInteractive={true}
        />
        
        {/* 小地图 */}
        {viewState.show_minimap && (
          <MiniMap 
            className="bg-zinc-900 border border-zinc-700"
            nodeColor="#00d4aa"
            maskColor="rgba(0, 0, 0, 0.6)"
            position="bottom-right"
          />
        )}

        {/* 顶部工具栏 */}
        <Panel position="top-left" className="flex items-center gap-2 p-2">
          {/* 布局切换 */}
          <div className="flex items-center gap-1 bg-zinc-900 rounded-lg border border-zinc-700 p-1">
            <Button
              variant={viewState.layout === 'force' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleLayoutChange('force')}
              className="h-8 w-8 p-0"
            >
              <Move3D className="h-4 w-4" />
            </Button>
            <Button
              variant={viewState.layout === 'hierarchical' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleLayoutChange('hierarchical')}
              className="h-8 w-8 p-0"
            >
              <GitBranch className="h-4 w-4" />
            </Button>
            <Button
              variant={viewState.layout === 'circular' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleLayoutChange('circular')}
              className="h-8 w-8 p-0"
            >
              <Circle className="h-4 w-4" />
            </Button>
            <Button
              variant={viewState.layout === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleLayoutChange('grid')}
              className="h-8 w-8 p-0"
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
          </div>

          {/* 视图控制 */}
          <div className="flex items-center gap-1 bg-zinc-900 rounded-lg border border-zinc-700 p-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomIn}
              className="h-8 w-8 p-0"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleZoomOut}
              className="h-8 w-8 p-0"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleFitView}
              className="h-8 w-8 p-0"
            >
              <Network className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetView}
              className="h-8 w-8 p-0"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={toggleFullscreen}
            className="h-8 w-8 p-0 bg-zinc-900 border border-zinc-700"
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </Panel>

        {/* 统计信息面板 */}
        <Panel position="top-right" className="flex items-center gap-2">
          <div className="flex items-center gap-2 bg-zinc-900/90 backdrop-blur-sm rounded-lg border border-zinc-700 px-3 py-2">
            <Badge variant="secondary" className="bg-[#00d4aa]/10 text-[#00d4aa] border-[#00d4aa]/20">
              {stats.totalNodes} 节点
            </Badge>
            <Badge variant="secondary" className="bg-blue-500/10 text-blue-400 border-blue-500/20">
              {stats.totalEdges} 关系
            </Badge>
            {(stats.selectedNodes > 0 || stats.selectedEdges > 0) && (
              <Badge variant="secondary" className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">
                已选择 {stats.selectedNodes + stats.selectedEdges}
              </Badge>
            )}
          </div>
        </Panel>
      </ReactFlow>
    </div>
  );
};

// 包装组件以提供ReactFlowProvider和错误边界
const GraphVisualization: React.FC<GraphVisualizationProps> = (props) => {
  const isMobile = useIsMobile();

  // 移动端使用专门的移动界面，但仍需要ReactFlowProvider
  if (isMobile) {
    return (
      <GraphErrorBoundary>
        <ReactFlowProvider>
          <MobileGraphInterface className={props.className} />
        </ReactFlowProvider>
      </GraphErrorBoundary>
    );
  }

  // 桌面端使用完整的图可视化
  return (
    <GraphErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Graph Visualization Error:', error, errorInfo);
        // 可以在这里发送错误报告到监控服务
      }}
      showErrorDetails={process.env.NODE_ENV === 'development'}
    >
      <ReactFlowProvider>
        <GraphVisualizationInner {...props} />
      </ReactFlowProvider>
    </GraphErrorBoundary>
  );
};

export default GraphVisualization;
